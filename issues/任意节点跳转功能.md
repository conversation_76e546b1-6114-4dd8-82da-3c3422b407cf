# 任意节点跳转功能实施记录

## 任务概述
为工作流系统添加跳转到任意节点的功能，允许用户在流程执行过程中跳转到流程中的任意用户任务节点。

## 实施方案
采用方案2：创建独立的任意节点跳转功能，不包含审计功能。

## 已完成的工作

### 1. 扩展TaskActionType枚举 ✅
- 文件: `services/hbut/src/main/java/org/ahead4/workflow/enums/TaskActionType.java`
- 添加了 `JUMP_TO_NODE("JUMP_TO_NODE", "跳转到任意节点")` 枚举值

### 2. 创建节点信息VO类 ✅
- 文件: `services/hbut/src/main/java/org/ahead4/workflow/domain/vo/WfNodeVo.java`
- 包含节点ID、名称、类型、是否可跳转等字段

### 3. 扩展IWfTaskService接口 ✅
- 文件: `services/hbut/src/main/java/org/ahead4/workflow/service/IWfTaskService.java`
- 添加了以下方法：
  - `void jumpToAnyNode(WfTaskBo bo)` - 跳转到任意节点
  - `List<WfNodeVo> getAvailableJumpNodes(WfTaskBo bo)` - 获取可跳转的节点列表

### 4. 实现WfTaskServiceImpl中的跳转逻辑 ✅
- 文件: `services/hbut/src/main/java/org/ahead4/workflow/service/impl/WfTaskServiceImpl.java`
- 实现了 `jumpToAnyNode` 方法：
  - 验证任务和流程实例状态
  - 验证目标节点的有效性（必须是用户任务节点）
  - 使用 `createChangeActivityStateBuilder()` 执行跳转
  - 添加跳转意见和处理抄送用户
- 实现了 `getAvailableJumpNodes` 方法：
  - 获取流程中所有用户任务节点
  - 排除当前节点
  - 返回可跳转的节点列表

### 5. 扩展WfTaskController控制器 ✅
- 文件: `services/hbut/src/main/java/org/ahead4/workflow/controller/WfTaskController.java`
- 在统一任务操作API中添加了 `JUMP_TO_NODE` 处理
- 添加了独立的接口：
  - `POST /workflow/task/jumpToNode` - 执行跳转操作
  - `POST /workflow/task/availableJumpNodes` - 获取可跳转节点列表

### 6. 更新相关注释 ✅
- 更新了 `WfTaskBo` 中的操作类型注释
- 更新了控制器中的API说明注释

## 核心功能特性

### 跳转功能特点
1. **安全验证**: 验证任务存在性、流程实例状态、目标节点有效性
2. **节点限制**: 只允许跳转到用户任务节点，确保流程的完整性
3. **意见记录**: 自动记录跳转操作的审批意见
4. **抄送处理**: 支持跳转后的抄送用户处理
5. **事务保护**: 使用 `@Transactional` 确保操作的原子性

### 节点查询特点
1. **全面覆盖**: 获取流程中所有用户任务节点（包括子流程中的节点）
2. **当前节点排除**: 自动排除当前正在执行的节点
3. **结构化返回**: 返回标准化的节点信息对象

## API使用示例

### 1. 获取可跳转节点列表
```http
POST /workflow/task/availableJumpNodes
Content-Type: application/json

{
    "taskId": "task123"
}
```

### 2. 执行节点跳转
```http
POST /workflow/task/jumpToNode
Content-Type: application/json

{
    "taskId": "task123",
    "procInstId": "proc456",
    "targetKey": "Activity_UserTask_2",
    "comment": "跳转到指定节点进行处理"
}
```

### 3. 使用统一操作API
```http
POST /workflow/task/action
Content-Type: application/json

{
    "actionType": "JUMP_TO_NODE",
    "taskId": "task123",
    "procInstId": "proc456",
    "targetKey": "Activity_UserTask_2",
    "comment": "跳转操作"
}
```

## 技术实现要点

1. **Flowable API使用**: 使用 `runtimeService.createChangeActivityStateBuilder()` 实现节点跳转
2. **BPMN模型解析**: 通过 `ModelUtils.getAllUserTaskEvent()` 获取所有用户任务节点
3. **异常处理**: 完善的异常处理机制，包括 `FlowableObjectNotFoundException` 和 `FlowableException`
4. **权限控制**: 继承现有的权限控制机制

## 状态
✅ 已完成 - 功能已实现并可以使用
