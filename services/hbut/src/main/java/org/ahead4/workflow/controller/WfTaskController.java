package org.ahead4.workflow.controller;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.web.presentate.HttpMsg;
import org.ahead4.workflow.domain.bo.WfTaskBo;
import org.ahead4.workflow.enums.TaskActionType;
import org.ahead4.workflow.service.IWfTaskService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;

/**
 * 工作流任务管理
 *
 * <AUTHOR>
 * @createTime 2022/3/10 00:12
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/workflow/task")
@Api(tags =  "工作流任务管理")
@PreAuthorize("hasAnyRole('ROLE_admin','ROLE_SOA', 'ROLE_Approver')")
public class WfTaskController {

    private final IWfTaskService flowTaskService;

    /**
     * 统一任务操作API
     * 根据actionType分发到对应的操作：
     * COMPLETE-审批通过, REJECT-拒绝, REJECT_PROCESS-驳回流程, RETURN-退回, CLAIM-认领, UNCLAIM-取消认领,
     * DELEGATE-委派, TRANSFER-转办, STOP-取消流程, REVOKE-撤回流程, DELETE-删除任务, JUMP_TO_NODE-跳转到任意节点
     */
    @ApiOperation(value = "统一任务操作")
    @PostMapping(value = "/action")
    public HttpMsg taskAction(@RequestBody WfTaskBo bo) {
        if (ObjectUtil.isEmpty(bo.getActionType())) {
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "操作类型不能为空！");
        }
        
        TaskActionType actionType = TaskActionType.fromCode(bo.getActionType());
        if (actionType == null) {
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "不支持的操作类型：" + bo.getActionType());
        }
        
        switch (actionType) {
            case COMPLETE:
                flowTaskService.complete(bo);
                break;
            case REJECT:
                flowTaskService.taskReject(bo);
                break;
            case REJECT_PROCESS:
                flowTaskService.rejectProcess(bo);
                break;
            case RETURN:
                flowTaskService.taskReturn(bo);
                break;
            case CLAIM:
                flowTaskService.claim(bo);
                break;
            case UNCLAIM:
                flowTaskService.unClaim(bo);
                break;
            case DELEGATE:
                if (ObjectUtil.hasNull(bo.getTaskId(), bo.getUserId())) {
                    return HttpMsg.error(HttpStatus.BAD_REQUEST, "委派操作需要任务ID和用户ID！");
                }
                flowTaskService.delegateTask(bo);
                break;
            case TRANSFER:
                if (ObjectUtil.hasNull(bo.getTaskId(), bo.getUserId())) {
                    return HttpMsg.error(HttpStatus.BAD_REQUEST, "转办操作需要任务ID和用户ID！");
                }
                flowTaskService.transferTask(bo);
                break;
            case STOP:
                flowTaskService.stopProcess(bo);
                break;
            case REVOKE:
                flowTaskService.revokeProcess(bo);
                break;
            case DELETE:
                flowTaskService.deleteTask(bo);
                break;
            case JUMP_TO_NODE:
                if (ObjectUtil.hasNull(bo.getTaskId(), bo.getTargetKey())) {
                    return HttpMsg.error(HttpStatus.BAD_REQUEST, "跳转操作需要任务ID和目标节点！");
                }
                flowTaskService.jumpToAnyNode(bo);
                break;
            default:
                return HttpMsg.error(HttpStatus.BAD_REQUEST, "不支持的操作类型：" + bo.getActionType());
        }
        
        return HttpMsg.ok();
    }

    /**
     * 取消流程
     */
    @PostMapping(value = "/stopProcess")
    public HttpMsg stopProcess(@RequestBody WfTaskBo bo) {
        flowTaskService.stopProcess(bo);
        return HttpMsg.ok();
    }

    /**
     * 撤回流程
     */
    @PostMapping(value = "/revokeProcess")
    public HttpMsg revokeProcess(@RequestBody WfTaskBo bo) {
        flowTaskService.revokeProcess(bo);
        return HttpMsg.ok();
    }

    /**
     * 获取流程变量
     * @param taskId 流程任务Id
     */
    @GetMapping(value = "/processVariables/{taskId}")
    public HttpMsg processVariables(@PathVariable(value = "taskId") String taskId) {
        return HttpMsg.ok().data(flowTaskService.getProcessVariables(taskId));
    }

    /**
     * 审批任务
     */
    @ApiOperation(value = "审批任务")
    @PostMapping(value = "/complete")
    public HttpMsg complete(@RequestBody WfTaskBo bo) {
        flowTaskService.complete(bo);
        return HttpMsg.ok();
    }

    /**
     * 拒绝任务
     */
    @ApiOperation(value = "拒绝任务")
    @PostMapping(value = "/reject")
    public HttpMsg taskReject(@RequestBody WfTaskBo taskBo) {
        flowTaskService.taskReject(taskBo);
        return HttpMsg.ok();
    }

    /**
     * 驳回流程
     */
    @ApiOperation(value = "驳回流程")
    @PostMapping(value = "/rejectProcess")
    public HttpMsg rejectProcess(@RequestBody WfTaskBo taskBo) {
        flowTaskService.rejectProcess(taskBo);
        return HttpMsg.ok();
    }

    /**
     * 退回任务
     */
    @PostMapping(value = "/return")
    public HttpMsg taskReturn(@RequestBody WfTaskBo bo) {
        flowTaskService.taskReturn(bo);
        return HttpMsg.ok();
    }

    /**
     * 获取所有可回退的节点
     */
    @PostMapping(value = "/returnList")
    public HttpMsg findReturnTaskList(@RequestBody WfTaskBo bo) {
        return HttpMsg.ok().data(flowTaskService.findReturnTaskList(bo));
    }

    /**
     * 删除任务
     */
    @DeleteMapping(value = "/delete")
    public HttpMsg delete(@RequestBody WfTaskBo bo) {
        flowTaskService.deleteTask(bo);
        return HttpMsg.ok();
    }

    /**
     * 认领/签收任务
     */
    @PostMapping(value = "/claim")
    public HttpMsg claim(@RequestBody WfTaskBo bo) {
        flowTaskService.claim(bo);
        return HttpMsg.ok();
    }
    /**
     * 取消认领/签收任务
     */
    @PostMapping(value = "/unClaim")
    public HttpMsg unClaim(@RequestBody WfTaskBo bo) {
        flowTaskService.unClaim(bo);
        return HttpMsg.ok();
    }

    /**
     * 委派任务
     */
    @PostMapping(value = "/delegate")
    public HttpMsg delegate(@RequestBody WfTaskBo bo) {
        if (ObjectUtil.hasNull(bo.getTaskId(), bo.getUserId())) {
            return HttpMsg.error(HttpStatus.BAD_REQUEST,"参数错误！");
        }
        flowTaskService.delegateTask(bo);
        return HttpMsg.ok();
    }

    /**
     * 转办任务
     */
    @ApiOperation(value = "转办任务")
    @PostMapping(value = "/transfer")
    public HttpMsg transfer(@RequestBody WfTaskBo bo) {
        if (ObjectUtil.hasNull(bo.getTaskId(), bo.getUserId())) {
            return HttpMsg.error("参数错误！");
        }
        flowTaskService.transferTask(bo);
        return HttpMsg.ok();
    }

    /**
     * 为多实例子流程添加动态加签
     */
    @PostMapping("/add-subprocess")
    public ResponseEntity<?> addSubProcess(
            @RequestParam String processInstanceId,
            @RequestBody List<String> newDepts) {

        flowTaskService.addSubProcesses(processInstanceId, newDepts);
        return ResponseEntity.ok().build();
    }

    /**
     * 生成流程图
     *
     * @param processId 任务ID
     */
    @RequestMapping("/diagram/{processId}")
    public void genProcessDiagram(HttpServletResponse response,
                                  @PathVariable("processId") String processId) {
        InputStream inputStream = flowTaskService.diagram(processId);
        OutputStream os = null;
        BufferedImage image = null;
        try {
            image = ImageIO.read(inputStream);
            response.setContentType("image/png");
            os = response.getOutputStream();
            if (image != null) {
                ImageIO.write(image, "png", os);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.flush();
                    os.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 跳转到任意节点
     */
    @ApiOperation(value = "跳转到任意节点")
    @PostMapping(value = "/jumpToNode")
    public HttpMsg jumpToNode(@RequestBody WfTaskBo bo) {
        if (ObjectUtil.hasNull(bo.getTaskId(), bo.getTargetKey())) {
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "任务ID和目标节点不能为空！");
        }
        flowTaskService.jumpToAnyNode(bo);
        return HttpMsg.ok();
    }

    /**
     * 获取可跳转的节点列表
     */
    @ApiOperation(value = "获取可跳转的节点列表")
    @PostMapping(value = "/availableJumpNodes")
    public HttpMsg getAvailableJumpNodes(@RequestBody WfTaskBo bo) {
        if (ObjectUtil.isEmpty(bo.getTaskId())) {
            return HttpMsg.error(HttpStatus.BAD_REQUEST, "任务ID不能为空！");
        }
        return HttpMsg.ok().data(flowTaskService.getAvailableJumpNodes(bo));
    }
}
