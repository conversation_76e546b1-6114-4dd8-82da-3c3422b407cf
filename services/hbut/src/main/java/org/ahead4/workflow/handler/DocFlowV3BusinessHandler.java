package org.ahead4.workflow.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.cdes.entity.DocFlow;
import org.ahead4.cdes.entity.dto.CCDto;
import org.ahead4.cdes.entity.dto.SmsSendRequest;
import org.ahead4.cdes.service.DocFlowService;
import org.ahead4.cdes.service.SmsService;
import org.ahead4.permission.entity.User;
import org.ahead4.permission.service.UserService;
import org.ahead4.workflow.spi.BizSystemAdapter;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.HistoryService;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公文流转V3版本业务处理器
 * 支持调用子流程模式，包含动态加签减签功能
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Slf4j
@Component("docFlowV3WfHandler")
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class DocFlowV3BusinessHandler implements TaskListener {

    private final DocFlowService docFlowService;
    private final BizSystemAdapter adapter;
    private final UserService userService;
    private final SmsService smsService;
    private final RuntimeService runtimeService;
    private final HistoryService historyService;

    /**
     * 更新业务状态
     */
    public void updateStatus(DelegateExecution execution) {
        FlowElement flowElement = execution.getCurrentFlowElement();
        if (ObjectUtil.isNotEmpty(flowElement) && flowElement instanceof UserTask) {
            UserTask userTask = (UserTask) flowElement;
            final String businessKey = execution.getProcessInstanceBusinessKey();
            String status = userTask.getDocumentation();
            if (Objects.nonNull(status)) {
                log.info("V3版本更新业务状态：{}, status: {}", businessKey, status);
                // 修改流程参数
                execution.setVariable("status", status);
                // 根据status修改业务数据
                docFlowService.update(null,
                        Wrappers.<DocFlow>lambdaUpdate().set(DocFlow::getStatus, status).eq(DocFlow::getId, businessKey));
            }
        }
    }

    /**
     * 任务通知处理（公共方法）
     */
    public void notifyTask(DelegateTask delegateTask) {
        final String processInstanceId = delegateTask.getProcessInstanceId();
        final String businessKey = (String) runtimeService.getVariable(processInstanceId, "id");
        final String name = delegateTask.getName();
        final String assignee = delegateTask.getAssignee();
        log.info("V3版本监听到{}任务分配：{}:{}:{}", name, processInstanceId, businessKey, assignee);

        final DocFlow docFlow = docFlowService.getById(businessKey);
        if (docFlow.getIsRemind() == 0) {
            log.warn("公文 {} 未配置发送消息", docFlow.getDocTitle());
            return;
        }

        // 发送短信通知
        sendSmsNotification(delegateTask, docFlow);
    }


    /**
     * 获取选中的去重后的部门列表
     *
     * @param execution 执行上下文
     * @return 部门列表
     */
    public List<String> getDistinctDepts(DelegateExecution execution, JSONArray ccDtos) {
        if (ObjectUtil.isEmpty(ccDtos)) {
            return Collections.emptyList();
        }
        List<CCDto> dtos = ccDtos.toJavaList(CCDto.class);
        return dtos.stream().map(CCDto::getDeptCode).distinct().collect(Collectors.toList());
    }

    /**
     * 获取动态部门负责人列表（适配调用子流程模式）
     * 返回leader类型的CCDto列表，包含完整的用户信息
     */
    public List<CCDto> getDynamicDepts(DelegateExecution execution, JSONArray ccDtos) {
        // 获取初始的leader类型CCDto列表
        List<CCDto> initialLeaders = getLeaderCCDtos(execution, ccDtos);

        // 检查是否有动态调整
        Object dynamicDeptsObj = execution.getVariable("dynamicDeptLeaders");
        if (dynamicDeptsObj == null) {
            // 第一次执行，保存初始leader列表和部门代码列表
            execution.setVariable("dynamicDeptLeaders", new ArrayList<>(initialLeaders));
            execution.setVariable("originalDeptLeaders", new ArrayList<>(initialLeaders));

            // 同时保存部门代码列表用于兼容性
            List<String> deptCodes = initialLeaders.stream()
                .map(CCDto::getDeptCode)
                .distinct()
                .collect(Collectors.toList());
            execution.setVariable("dynamicDepts", new ArrayList<>(deptCodes));
            execution.setVariable("originalDepts", new ArrayList<>(deptCodes));

            return initialLeaders;
        }

        // 返回动态调整后的leader列表
        @SuppressWarnings("unchecked")
        List<CCDto> dynamicLeaders = (List<CCDto>) dynamicDeptsObj;
        return dynamicLeaders;
    }

    /**
     * 获取leader类型的CCDto列表
     */
    private List<CCDto> getLeaderCCDtos(DelegateExecution execution, JSONArray ccDtos) {
        if (ObjectUtil.isEmpty(ccDtos)) {
            return Collections.emptyList();
        }

        List<CCDto> dtos = ccDtos.toJavaList(CCDto.class);
        return dtos.stream()
            .filter(dto -> "leader".equals(dto.getMarkCode()))
            .collect(Collectors.toList());
    }


    /**
     * 根据部门代码获取用户（保持向后兼容性）
     * @param execution 执行
     * @param deptCode  部门代码
     * @param ccDtos    抄送 DTO
     * @param markCode  用户类型
     * @return {@link List }<{@link CCDto }>
     */
    public List<CCDto> getUserByDeptCode(DelegateExecution execution, String deptCode, JSONArray ccDtos, String markCode) {
        // 优先从传入的deptLeader参数获取
        Object deptLeaderObj = execution.getVariable("deptLeader");
        if (deptLeaderObj instanceof CCDto) {
            CCDto deptLeader = (CCDto) deptLeaderObj;
            if (deptCode.equals(deptLeader.getDeptCode()) &&
                (StringUtils.isBlank(markCode) || "leader".equals(markCode) || markCode.equals(deptLeader.getMarkCode()))) {
                return Collections.singletonList(deptLeader);
            }
        }

        // 兼容原有逻辑
        List<CCDto> dtos = ccDtos.toJavaList(CCDto.class);
        if (ObjectUtil.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        if (StringUtils.isBlank(markCode)) {
            markCode = "leader";
        }
        // list 转 map<deptCode, List<CCDto>>
        // 按照部门分组
        String finalMarkCode = markCode;
        Map<String, List<CCDto>> deptMap = dtos.stream()
            .filter(c -> c.getMarkCode().equals(finalMarkCode))
            .collect(Collectors.groupingBy(CCDto::getDeptCode, LinkedHashMap::new, Collectors.toList()));
        return deptMap.getOrDefault(deptCode, Collections.emptyList());
    }
    /**
     * 根据部门代码获取部门名称
     */
    private String getDeptNameByCode(String deptCode) {
        // 这里应该调用实际的部门服务获取部门名称
        // 暂时返回默认格式
        log.info("根据部门代码获取部门名称：{}", deptCode);
        String deptName = adapter.getDeptNameById(deptCode);
        return deptName;
    }

    /**
     * 检查调用子流程的完成条件
     */
    public boolean checkCallActivityCompletion(DelegateExecution execution,
                                             int nrOfCompletedInstances,
                                             int nrOfInstances,
                                             int nrOfActiveInstances) {
        try {
            log.info("检查调用子流程完成条件: 已完成={}, 总数={}, 活跃数={}", 
                    nrOfCompletedInstances, nrOfInstances, nrOfActiveInstances);

            // 检查是否有动态调整
            if (hasDynamicDeptChanges(execution)) {
                return handleDynamicCompletion(execution, nrOfCompletedInstances, nrOfInstances, nrOfActiveInstances);
            }

            // 默认完成条件：所有实例都完成
            return nrOfActiveInstances == 0;
            
        } catch (Exception e) {
            log.error("检查调用子流程完成条件时发生异常", e);
            return false;
        }
    }

    /**
     * 处理动态调整的完成条件
     */
    private boolean handleDynamicCompletion(DelegateExecution execution,
                                          int nrOfCompletedInstances,
                                          int nrOfInstances,
                                          int nrOfActiveInstances) {
        // 获取当前动态部门负责人列表
        @SuppressWarnings("unchecked")
        List<CCDto> currentLeaders = (List<CCDto>) execution.getVariable("dynamicDeptLeaders");
        if (currentLeaders != null) {
            int currentRequiredInstances = currentLeaders.size();

            // 减签情况：如果完成数已满足新要求
            if (nrOfCompletedInstances >= currentRequiredInstances) {
                log.info("减签后完成条件已满足: 完成数={}, 要求数={}", nrOfCompletedInstances, currentRequiredInstances);
                resetDynamicDeptFlag(execution);
                return true;
            }
        }

        return nrOfActiveInstances == 0;
    }

    /**
     * 检查是否有动态部门变更
     */
    public boolean hasDynamicDeptChanges(DelegateExecution execution) {
        final Object updated = execution.getVariable("dynamicDeptsUpdated");
        return Boolean.TRUE.equals(updated);
    }

    /**
     * 重置动态部门变更标志
     */
    public void resetDynamicDeptFlag(DelegateExecution execution) {
        execution.setVariable("dynamicDeptsUpdated", false);
    }

    /**
     * 发送短信通知
     */
    private void sendSmsNotification(DelegateTask delegateTask, DocFlow docFlow) {
        try {
            final String assignee = delegateTask.getAssignee();
            if (StringUtils.isBlank(assignee)) {
                log.warn("任务 {} 没有指定处理人，跳过短信通知", delegateTask.getName());
                return;
            }

            final User user = userService.getById(assignee);
            if (user == null || StringUtils.isBlank(user.getMobile())) {
                log.warn("用户 {} 不存在或没有手机号，跳过短信通知", assignee);
                return;
            }

            SmsSendRequest smsRequest = new SmsSendRequest();
            smsRequest.setPhoneNumbers(user.getMobile());
            smsRequest.setMessageContent(String.format("您有新的公文需要处理：%s，请及时登录系统处理。", docFlow.getDocTitle()));
            
            smsService.sendSms(smsRequest);
            log.info("已向用户 {} 发送短信通知", user.getUsername());
            
        } catch (Exception e) {
            log.error("发送短信通知失败", e);
        }
    }

    /**
     * 动态加签部门（支持CCDto）
     */
    public boolean addSignDepts(String processInstanceId, List<CCDto> newDeptLeaders) {
        try {
            log.info("开始为流程 {} 加签部门负责人: {}", processInstanceId,
                newDeptLeaders.stream().map(CCDto::getDeptName).collect(Collectors.toList()));

            // 获取当前动态部门负责人列表
            @SuppressWarnings("unchecked")
            List<CCDto> currentLeaders = (List<CCDto>) runtimeService.getVariable(processInstanceId, "dynamicDeptLeaders");
            if (currentLeaders == null) {
                currentLeaders = new ArrayList<>();
            }

            // 添加新的部门负责人
            currentLeaders.addAll(newDeptLeaders);

            // 更新部门代码列表（兼容性）
            @SuppressWarnings("unchecked")
            List<String> currentDepts = (List<String>) runtimeService.getVariable(processInstanceId, "dynamicDepts");
            if (currentDepts == null) {
                currentDepts = new ArrayList<>();
            }
            List<String> newDeptCodes = newDeptLeaders.stream()
                .map(CCDto::getDeptCode)
                .collect(Collectors.toList());
            currentDepts.addAll(newDeptCodes);

            // 更新流程变量
            runtimeService.setVariable(processInstanceId, "dynamicDeptLeaders", currentLeaders);
            runtimeService.setVariable(processInstanceId, "dynamicDepts", currentDepts);
            runtimeService.setVariable(processInstanceId, "dynamicDeptsUpdated", true);
            runtimeService.setVariable(processInstanceId, "addSignDeptLeaders", newDeptLeaders);

            // 为新增部门启动调用子流程实例
            startAddSignCallActivities(processInstanceId, newDeptCodes);

            log.info("成功为流程 {} 加签 {} 个部门", processInstanceId, newDeptLeaders.size());
            return true;

        } catch (Exception e) {
            log.error("加签部门失败", e);
            return false;
        }
    }

    /**
     * 动态加签部门（兼容原有接口）
     */
    public boolean addSignDeptsByCode(String processInstanceId, List<String> newDepts) {
        // 将部门代码转换为CCDto（需要查询用户信息）
        List<CCDto> newDeptLeaders = newDepts.stream()
            .map(deptCode -> {
                CCDto leader = new CCDto();
                leader.setDeptCode(deptCode);
                leader.setDeptName(getDeptNameByCode(deptCode));
                leader.setMarkCode("leader");
                // 这里应该查询实际的部门负责人信息
                leader.setUsername("leader_" + deptCode);
                leader.setDisplayname("部门负责人_" + deptCode);
                return leader;
            })
            .collect(Collectors.toList());

        return addSignDepts(processInstanceId, newDeptLeaders);
    }

    /**
     * 动态减签部门（支持CCDto）
     */
    public boolean removeSignDepts(String processInstanceId, List<CCDto> removeDeptLeaders) {
        try {
            log.info("开始为流程 {} 减签部门负责人: {}", processInstanceId,
                removeDeptLeaders.stream().map(CCDto::getDeptName).collect(Collectors.toList()));

            // 获取当前动态部门负责人列表
            @SuppressWarnings("unchecked")
            List<CCDto> currentLeaders = (List<CCDto>) runtimeService.getVariable(processInstanceId, "dynamicDeptLeaders");
            if (currentLeaders == null) {
                log.warn("当前没有动态部门负责人列表，无法减签");
                return false;
            }

            // 移除指定的部门负责人
            Set<String> removeDeptCodes = removeDeptLeaders.stream()
                .map(CCDto::getDeptCode)
                .collect(Collectors.toSet());
            currentLeaders.removeIf(leader -> removeDeptCodes.contains(leader.getDeptCode()));

            // 更新部门代码列表（兼容性）
            @SuppressWarnings("unchecked")
            List<String> currentDepts = (List<String>) runtimeService.getVariable(processInstanceId, "dynamicDepts");
            if (currentDepts != null) {
                currentDepts.removeAll(removeDeptCodes);
            }

            // 更新流程变量
            runtimeService.setVariable(processInstanceId, "dynamicDeptLeaders", currentLeaders);
            runtimeService.setVariable(processInstanceId, "dynamicDepts", currentDepts);
            runtimeService.setVariable(processInstanceId, "dynamicDeptsUpdated", true);
            runtimeService.setVariable(processInstanceId, "removeSignDeptLeaders", removeDeptLeaders);

            // 终止对应的调用子流程实例
            terminateRemoveSignCallActivities(processInstanceId, new ArrayList<>(removeDeptCodes));

            log.info("成功为流程 {} 减签 {} 个部门", processInstanceId, removeDeptLeaders.size());
            return true;

        } catch (Exception e) {
            log.error("减签部门失败", e);
            return false;
        }
    }

    /**
     * 动态减签部门（兼容原有接口）
     */
    public boolean removeSignDeptsByCode(String processInstanceId, List<String> removeDepts) {
        try {
            // 获取当前动态部门负责人列表，找到对应的CCDto
            @SuppressWarnings("unchecked")
            List<CCDto> currentLeaders = (List<CCDto>) runtimeService.getVariable(processInstanceId, "dynamicDeptLeaders");
            if (currentLeaders == null) {
                log.warn("当前没有动态部门负责人列表，无法减签");
                return false;
            }

            // 找到要移除的部门负责人
            List<CCDto> removeDeptLeaders = currentLeaders.stream()
                .filter(leader -> removeDepts.contains(leader.getDeptCode()))
                .collect(Collectors.toList());

            return removeSignDepts(processInstanceId, removeDeptLeaders);

        } catch (Exception e) {
            log.error("减签部门失败", e);
            return false;
        }
    }

    /**
     * 为加签部门启动调用子流程实例
     * 注意：在调用子流程模式下，加签需要通过动态修改多实例集合来实现
     */
    private void startAddSignCallActivities(String parentProcessInstanceId, List<String> newDepts) {
        try {
            log.info("为流程 {} 动态加签部门: {}", parentProcessInstanceId, newDepts);

            // 在调用子流程模式下，加签是通过修改多实例集合实现的
            // 这里主要是记录加签操作，实际的子流程启动由多实例机制处理

            // 记录加签操作到流程变量
            runtimeService.setVariable(parentProcessInstanceId, "lastAddSignTime", new Date());
            runtimeService.setVariable(parentProcessInstanceId, "lastAddSignDepts", newDepts);

            log.info("已记录加签操作，多实例机制将自动启动新的调用子流程");

        } catch (Exception e) {
            log.error("记录加签操作失败: {}", e.getMessage());
        }
    }

    /**
     * 终止减签部门的调用子流程实例
     * 在调用子流程模式下，减签通过修改多实例集合和终止对应的执行实例来实现
     */
    private void terminateRemoveSignCallActivities(String parentProcessInstanceId, List<String> removeDepts) {
        try {
            log.info("为流程 {} 动态减签部门: {}", parentProcessInstanceId, removeDepts);

            // 查找并终止对应的调用子流程实例
            for (String deptCode : removeDepts) {
                List<ProcessInstance> subProcesses = runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey("doc_flow_v3_subprocess")
                        .variableValueEquals("parentProcessInstanceId", parentProcessInstanceId)
                        .variableValueEquals("deptCode", deptCode)
                        .active()
                        .list();

                for (ProcessInstance subProcess : subProcesses) {
                    runtimeService.deleteProcessInstance(subProcess.getId(), "减签操作");
                    log.info("已终止部门 {} 的调用子流程: {}", deptCode, subProcess.getId());
                }
            }

            // 记录减签操作到流程变量
            runtimeService.setVariable(parentProcessInstanceId, "lastRemoveSignTime", new Date());
            runtimeService.setVariable(parentProcessInstanceId, "lastRemoveSignDepts", removeDepts);

            log.info("已完成减签操作");

        } catch (Exception e) {
            log.error("减签操作失败: {}", e.getMessage());
        }
    }

    /**
     * 根据部门代码获取部门负责人
     */
    public List<Map<String, Object>> getDeptHeadByDeptCode(DelegateExecution execution, String deptCode, String activityId, String contactType) {
        try {
            log.info("获取部门 {} 的 {} 类型联系人", deptCode, contactType);

            // 这里应该调用实际的用户服务获取联系人列表
            // 暂时返回模拟数据
            List<Map<String, Object>> contacts = new ArrayList<>();
            Map<String, Object> contact = new HashMap<>();
            contact.put("username", "contact_" + deptCode);
            contact.put("deptName", getDeptNameByCode(deptCode));
            contact.put("deptCode", deptCode);
            contacts.add(contact);

            return contacts;
        } catch (Exception e) {
            log.error("获取部门联系人失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取调用子流程相关的部门联络员
     * 从已完成的调用子流程实例中获取部门信息，然后查找对应的联络员
     */
    public List<Map<String, Object>> getDeptContactsForCallActivity(DelegateExecution execution, String callActivityId) {
        try {
            log.info("获取调用子流程 {} 相关的部门联络员", callActivityId);

            // 优先从动态部门负责人列表获取
            @SuppressWarnings("unchecked")
            List<CCDto> dynamicLeaders = (List<CCDto>) execution.getVariable("dynamicDeptLeaders");
            if (dynamicLeaders != null && !dynamicLeaders.isEmpty()) {
                List<Map<String, Object>> contacts = new ArrayList<>();
                // 获取已完成的部门，查找对应的联络员
                Set<String> completedDepts = getCompletedDeptCodes(execution);

                for (String deptCode : completedDepts) {
                    Map<String, Object> contact = new HashMap<>();
                    contact.put("username", "contact_" + deptCode);
                    contact.put("deptName", getDeptNameByCode(deptCode));
                    contact.put("deptCode", deptCode);
                    contacts.add(contact);
                }

                log.info("为 {} 个已完成部门获取了联络员信息", contacts.size());
                return contacts;
            }

            // 兼容原有逻辑
            @SuppressWarnings("unchecked")
            List<String> dynamicDepts = (List<String>) execution.getVariable("dynamicDepts");
            if (dynamicDepts == null || dynamicDepts.isEmpty()) {
                log.warn("未找到动态部门列表");
                return new ArrayList<>();
            }

            List<Map<String, Object>> contacts = new ArrayList<>();
            for (String deptCode : dynamicDepts) {
                Map<String, Object> contact = new HashMap<>();
                contact.put("username", "contact_" + deptCode);
                contact.put("deptName", getDeptNameByCode(deptCode));
                contact.put("deptCode", deptCode);
                contacts.add(contact);
            }

            log.info("为 {} 个部门获取了联络员信息", contacts.size());
            return contacts;
        } catch (Exception e) {
            log.error("获取调用子流程部门联络员失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取已完成的部门代码列表
     */
    private Set<String> getCompletedDeptCodes(DelegateExecution execution) {
        try {
            String processInstanceId = execution.getProcessInstanceId();

            // 查询已完成的调用子流程实例
            List<org.flowable.engine.history.HistoricProcessInstance> completedSubProcesses =
                historyService.createHistoricProcessInstanceQuery()
                    .processDefinitionKey("doc_flow_v3_subprocess")
                    .variableValueEquals("parentProcessInstanceId", processInstanceId)
                    .finished()
                    .list();

            return completedSubProcesses.stream()
                    .map(pi -> (String) historyService.createHistoricVariableInstanceQuery()
                        .processInstanceId(pi.getId())
                        .variableName("deptCode")
                        .singleResult().getValue())
                    .filter(Objects::nonNull)
                    .map(String::valueOf)
                    .collect(Collectors.toSet());

        } catch (Exception e) {
            log.error("获取已完成部门代码失败", e);
            return Collections.emptySet();
        }
    }

    /**
     * 获取用户ID列表
     */
    public List<String> getUserIds(DelegateExecution execution) {
        try {
            // 这里应该根据实际业务逻辑获取用户ID列表
            // 暂时返回模拟数据
            List<String> userIds = new ArrayList<>();
            userIds.add("admin");
            userIds.add("user1");
            return userIds;
        } catch (Exception e) {
            log.error("获取用户ID列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public void notify(DelegateTask delegateTask) {
        // 调用公共的通知方法
        notifyTask(delegateTask);
    }

    /**
     * 处理超时检查
     */
    public boolean checkTimeout(DelegateExecution execution, int nrOfCompletedInstances, int nrOfActiveInstances) {
        try {
            // 获取超时配置
            Object timeoutMinutes = execution.getVariable("timeoutMinutes");
            Object startTimeObj = execution.getVariable("startTime");

            if (timeoutMinutes == null || startTimeObj == null) {
                return false; // 没有配置超时，继续等待
            }

            long startTime = Long.parseLong(startTimeObj.toString());
            long currentTime = System.currentTimeMillis();
            long timeoutMs = Long.parseLong(timeoutMinutes.toString()) * 60 * 1000;

            if (currentTime - startTime > timeoutMs) {
                log.warn("调用子流程执行超时，已完成: {}, 仍在执行: {}", nrOfCompletedInstances, nrOfActiveInstances);
                // 超时了，如果有完成的就算完成，否则继续等待
                return nrOfCompletedInstances > 0;
            }

            return false; // 未超时，继续等待
        } catch (Exception e) {
            log.warn("检查超时条件时发生异常: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取当前活跃的调用子流程实例数量
     */
    public int getActiveCallActivityCount(DelegateExecution execution) {
        try {
            final String processInstanceId = execution.getProcessInstanceId();

            // 查询当前活跃的调用子流程实例
            List<ProcessInstance> activeSubProcesses = runtimeService.createProcessInstanceQuery()
                    .processDefinitionKey("doc_flow_v3_subprocess")
                    .variableValueEquals("parentProcessInstanceId", processInstanceId)
                    .active()
                    .list();

            return activeSubProcesses.size();
        } catch (Exception e) {
            log.error("获取活跃调用子流程数量失败", e);
            return 0;
        }
    }

    /**
     * 处理加签完成检查
     */
    private boolean handleAddSignCompletion(DelegateExecution execution, int nrOfCompletedInstances, int nrOfInstances) {
        try {
            // 获取加签部门列表
            @SuppressWarnings("unchecked")
            List<String> addSignDepts = (List<String>) execution.getVariable("addSignDepts");
            if (addSignDepts == null || addSignDepts.isEmpty()) {
                return false;
            }

            // 检查加签部门的子流程是否都完成
            String processInstanceId = execution.getProcessInstanceId();
            for (String deptCode : addSignDepts) {
                List<ProcessInstance> deptSubProcesses = runtimeService.createProcessInstanceQuery()
                        .processDefinitionKey("doc_flow_v3_subprocess")
                        .variableValueEquals("parentProcessInstanceId", processInstanceId)
                        .variableValueEquals("deptCode", deptCode)
                        .variableValueEquals("isAddSignProcess", true)
                        .active()
                        .list();

                if (!deptSubProcesses.isEmpty()) {
                    // 还有加签部门的子流程在运行
                    return false;
                }
            }

            // 所有加签部门的子流程都完成了
            log.info("所有加签部门的子流程都已完成");
            return true;

        } catch (Exception e) {
            log.error("检查加签完成状态时发生异常", e);
            return false;
        }
    }
}
