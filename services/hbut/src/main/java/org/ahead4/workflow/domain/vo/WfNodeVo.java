package org.ahead4.workflow.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 工作流节点信息视图对象
 *
 * <AUTHOR>
 * @createTime 2024/01/01 00:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "工作流节点信息", description = "工作流节点信息视图对象")
public class WfNodeVo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 节点ID
     */
    @ApiModelProperty(value = "节点ID")
    private String nodeId;
    
    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称")
    private String nodeName;
    
    /**
     * 节点类型
     */
    @ApiModelProperty(value = "节点类型")
    private String nodeType;
    
    /**
     * 是否可跳转
     */
    @ApiModelProperty(value = "是否可跳转")
    private Boolean canJump;
    
    /**
     * 节点描述
     */
    @ApiModelProperty(value = "节点描述")
    private String description;
    
    /**
     * 是否为当前节点
     */
    @ApiModelProperty(value = "是否为当前节点")
    private Boolean isCurrent;
}
