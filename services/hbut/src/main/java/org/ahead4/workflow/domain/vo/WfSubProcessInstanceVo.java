package org.ahead4.workflow.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 子流程实例视图对象
 * 用于表示并行多实例子流程中的单个实例及其包含的任务节点
 *
 * <AUTHOR>
 * @createTime 2024/01/01 00:00
 */
@Data
@ExcelIgnoreUnannotated
@ApiModel(value = "子流程实例视图对象")
public class WfSubProcessInstanceVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子流程实例ID
     */
    @ApiModelProperty(value = "子流程实例ID")
    private String instanceId;

    /**
     * 流程实例ID（用于新接口）
     */
    @ApiModelProperty(value = "流程实例ID")
    private String processInstanceId;

    /**
     * 流程实例名称（用于新接口）
     */
    @ApiModelProperty(value = "流程实例名称")
    private String processInstanceName;

    /**
     * 子流程实例名称
     */
    @ApiModelProperty(value = "子流程实例名称")
    private String instanceName;

    /**
     * 父执行ID
     */
    @ApiModelProperty(value = "父执行ID")
    private String parentExecutionId;

    /**
     * 实例索引（在多实例中的序号）
     */
    @ApiModelProperty(value = "实例索引")
    private Integer instanceIndex;

    /**
     * 实例状态：running-运行中, completed-已完成, terminated-已终止
     */
    @ApiModelProperty(value = "实例状态")
    private String instanceStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 开始时间（用于新接口）
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 实例耗时
     */
    @ApiModelProperty(value = "实例耗时")
    private String duration;

    /**
     * 实例耗时（毫秒，用于新接口）
     */
    @ApiModelProperty(value = "实例耗时毫秒")
    private Long durationInMillis;

    /**
     * 执行人Id
     */
    @ApiModelProperty(value = "执行人Id")
    private String assigneeId;
    /**
     * 执行人名称
     */
    @ApiModelProperty(value = "执行人名称")
    private String assigneeName;

    /**
     * 是否为当前活动实例
     */
    @ApiModelProperty(value = "是否为当前活动实例")
    private Boolean isActive;

    /**
     * 子流程实例中的任务节点列表
     */
    @ApiModelProperty(value = "子流程实例中的任务节点列表")
    private List<WfProcNodeVo> taskNodes;

    /**
     * 实例变量（如果需要的话）
     */
    @ApiModelProperty(value = "实例变量")
    private Object instanceVariables;

    /**
     * 部门编码
     */
    @ApiModelProperty(value = "部门代编码")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String deptName;

    /**
     * 任务 ID
     */
    @ApiModelProperty(value = "任务 ID")
    private String taskId;

    /**
     * 执行 ID
     */
    @ApiModelProperty(value = "执行 ID")
    private String executionId;

    /**
     * 待办任务列表（仅运行中的子流程实例）
     */
    @ApiModelProperty(value = "待办任务列表")
    private List<WfTaskVo> pendingTasks;
}
