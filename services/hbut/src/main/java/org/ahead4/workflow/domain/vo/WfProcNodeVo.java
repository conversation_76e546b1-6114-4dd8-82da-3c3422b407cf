package org.ahead4.workflow.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.ahead4.cdes.entity.dto.FlowComment;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 工作流节点元素视图对象
 *
 * <AUTHOR>
 * @createTime 2022/9/11 22:04
 */
@Data
@ExcelIgnoreUnannotated
@ApiModel(value = "工作流节点元素视图对象")
public class WfProcNodeVo implements Serializable {
    /**
     * 流程ID
     */
    @ApiModelProperty(value = "流程ID")
    private String procDefId;
    /**
     * 活动ID
     */
    @ApiModelProperty(value = "活动ID")
    private String activityId;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    /**
     * 活动类型
     */
    @ApiModelProperty(value = "活动类型")
    private String activityType;
    /**
     * 活动耗时
     */
    @ApiModelProperty(value = "活动耗时")
    private String duration;
    /**
     * 执行人Id
     */
    @ApiModelProperty(value = "执行人Id")
    private String assigneeId;
    /**
     * 执行人名称
     */
    @ApiModelProperty(value = "执行人名称")
    private String assigneeName;
    /**
     * 候选执行人
     */
    @ApiModelProperty(value = "候选执行人")
    private String candidate;
    /**
     * 任务意见
     */
    @ApiModelProperty(value = "任务意见")
    private List<FlowComment> commentList = new ArrayList<>();
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 是否为当前活动任务
     */
    @ApiModelProperty(value = "是否为当前活动任务")
    private Boolean isActive;

    /**
     * 文档
     */
    @ApiModelProperty(value = "节点描述")
    private String documentation;

    /**
     * 子流程节点列表（仅当节点类型为子流程时使用）
     * 对于并行多实例子流程，每个实例的任务会按实例分组
     */
    @ApiModelProperty(value = "子流程节点列表")
    private List<WfSubProcessInstanceVo> subProcessInstances;

    /**
     * 子流程实例数量（精简模式下使用）
     */
    @ApiModelProperty(value = "子流程实例数量")
    private Integer subProcessInstanceCount;
}
