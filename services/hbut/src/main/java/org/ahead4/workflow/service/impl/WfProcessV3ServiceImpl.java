package org.ahead4.workflow.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.ahead4.web.exception.RestException;
import org.ahead4.workflow.domain.common.PageQuery;
import org.ahead4.workflow.domain.page.TableDataInfo;
import org.ahead4.workflow.domain.vo.WfProcNodeVo;
import org.ahead4.workflow.domain.vo.WfSubProcessInstanceVo;
import org.ahead4.workflow.domain.vo.WfTaskVo;
import org.ahead4.workflow.handler.DocFlowV3BusinessHandler;
import org.ahead4.workflow.service.IWfProcessV3Service;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.constants.BpmnXMLConstants;
import org.flowable.bpmn.model.*;
import org.flowable.bpmn.model.Process;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricActivityInstanceQuery;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工作流流程服务V3实现类
 * 专门处理调用子流程模式的工作流
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WfProcessV3ServiceImpl implements IWfProcessV3Service {

    private final HistoryService historyService;
    private final RuntimeService runtimeService;
    private final TaskService taskService;
    private final RepositoryService repositoryService;
    private final DocFlowV3BusinessHandler docFlowV3BusinessHandler;

    @Override
    public List<WfProcNodeVo> queryProcessDetailsWithCallActivityHandling(String procInstId) {
        HistoricProcessInstance historicProcIns = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(procInstId)
                .includeProcessVariables()
                .singleResult();
        if (historicProcIns == null) {
            throw new RestException("流程实例不存在");
        }

        // 获取BPMN模型
        BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcIns.getProcessDefinitionId());
        Process mainProcess = bpmnModel.getMainProcess();

        // 获取所有历史活动实例
        List<HistoricActivityInstance> allHistoricActivities = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(procInstId)
                .orderByHistoricActivityInstanceStartTime()
                .asc()
                .list();

        // 获取所有活跃任务
        List<Task> allActiveTasks = taskService.createTaskQuery()
                .processInstanceId(procInstId)
                .active()
                .list();

        List<WfProcNodeVo> nodeList = new ArrayList<>();

        // 按流程定义顺序处理所有流程元素
        Collection<FlowElement> flowElements = mainProcess.getFlowElements();
        for (FlowElement flowElement : flowElements) {
            if (flowElement instanceof UserTask) {
                WfProcNodeVo userTaskNode = processUserTaskNode((UserTask) flowElement, allHistoricActivities, allActiveTasks);
                if (userTaskNode != null) {
                    nodeList.add(userTaskNode);
                }
            } else if (flowElement instanceof CallActivity) {
                WfProcNodeVo callActivityNode = processCallActivityNode((CallActivity) flowElement, historicProcIns, allHistoricActivities, allActiveTasks);
                if (callActivityNode != null) {
                    nodeList.add(callActivityNode);
                }
            }
        }

        return nodeList;
    }

    /**
     * 处理用户任务节点
     */
    private WfProcNodeVo processUserTaskNode(UserTask userTask, List<HistoricActivityInstance> allHistoricActivities, List<Task> allActiveTasks) {
        WfProcNodeVo nodeVo = new WfProcNodeVo();
        nodeVo.setActivityId(userTask.getId());
        nodeVo.setActivityName(userTask.getName());
        nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_TASK_USER);

        // 查找对应的历史活动实例
        List<HistoricActivityInstance> taskHistories = allHistoricActivities.stream()
                .filter(h -> userTask.getId().equals(h.getActivityId()) && BpmnXMLConstants.ELEMENT_TASK_USER.equals(h.getActivityType()))
                .collect(Collectors.toList());

        // 查找对应的活跃任务
        List<Task> activeTasks = allActiveTasks.stream()
                .filter(t -> userTask.getId().equals(t.getTaskDefinitionKey()))
                .collect(Collectors.toList());

        if (!taskHistories.isEmpty()) {
            HistoricActivityInstance latestHistory = taskHistories.get(taskHistories.size() - 1);
            nodeVo.setCreateTime(latestHistory.getStartTime());
            nodeVo.setEndTime(latestHistory.getEndTime());
            
            if (latestHistory.getEndTime() != null) {
                nodeVo.setIsActive(false);
//                nodeVo.setNodeStatus(ProcessConstants.STATUS_FINISH);
            } else {
                nodeVo.setIsActive(true);
//                nodeVo.setNodeStatus(ProcessConstants.STATUS_RUNNING);
            }
        } else if (!activeTasks.isEmpty()) {
            nodeVo.setIsActive(true);
//            nodeVo.setNodeStatus(ProcessConstants.STATUS_RUNNING);
            nodeVo.setCreateTime(activeTasks.get(0).getCreateTime());
        } else {
            nodeVo.setIsActive(false);
//            nodeVo.setNodeStatus(ProcessConstants.STATUS_WAITING);
        }

        return nodeVo;
    }

    /**
     * 处理调用活动节点（调用子流程）
     */
    private WfProcNodeVo processCallActivityNode(CallActivity callActivity, HistoricProcessInstance historicProcIns, 
                                                List<HistoricActivityInstance> allHistoricActivities, List<Task> allActiveTasks) {
        WfProcNodeVo nodeVo = new WfProcNodeVo();
        nodeVo.setActivityId(callActivity.getId());
        nodeVo.setActivityName(callActivity.getName());
        nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_CALL_ACTIVITY);

        // 检查是否是多实例调用活动
        MultiInstanceLoopCharacteristics loopChars = callActivity.getLoopCharacteristics();
        if (loopChars != null) {
            return processMultiInstanceCallActivityNode(callActivity, historicProcIns, allHistoricActivities, allActiveTasks);
        }

        // 处理单个调用活动
        List<HistoricActivityInstance> callActivityHistories = allHistoricActivities.stream()
                .filter(h -> callActivity.getId().equals(h.getActivityId()))
                .collect(Collectors.toList());

        if (!callActivityHistories.isEmpty()) {
            HistoricActivityInstance latestHistory = callActivityHistories.get(callActivityHistories.size() - 1);
            nodeVo.setCreateTime(latestHistory.getStartTime());
            nodeVo.setEndTime(latestHistory.getEndTime());
            
            if (latestHistory.getEndTime() != null) {
                nodeVo.setIsActive(false);
//                nodeVo.setNodeStatus(ProcessConstants.STATUS_FINISH);
            } else {
                nodeVo.setIsActive(true);
//                nodeVo.setNodeStatus(ProcessConstants.STATUS_RUNNING);
            }
        } else {
            nodeVo.setIsActive(false);
//            nodeVo.setNodeStatus(ProcessConstants.STATUS_WAITING);
        }

        return nodeVo;
    }

    /**
     * 处理多实例调用活动节点
     */
    private WfProcNodeVo processMultiInstanceCallActivityNode(CallActivity callActivity, HistoricProcessInstance historicProcIns,
                                                            List<HistoricActivityInstance> allHistoricActivities, List<Task> allActiveTasks) {
        WfProcNodeVo nodeVo = new WfProcNodeVo();
        nodeVo.setActivityId(callActivity.getId());
        nodeVo.setActivityName(callActivity.getName());
        nodeVo.setActivityType(BpmnXMLConstants.ELEMENT_CALL_ACTIVITY);

        MultiInstanceLoopCharacteristics loopChars = callActivity.getLoopCharacteristics();
        if (loopChars == null || StringUtils.isBlank(loopChars.getElementVariable())) {
            return nodeVo;
        }

        String elementVariable = loopChars.getElementVariable();

        // 查找所有调用活动实例
        List<HistoricActivityInstance> callActivityInstances = allHistoricActivities.stream()
                .filter(h -> callActivity.getId().equals(h.getActivityId()))
                .collect(Collectors.toList());

        // 判断是否活跃
        nodeVo.setIsActive(callActivityInstances.stream().anyMatch(h -> h.getStartTime() != null && h.getEndTime() == null));

        if (callActivityInstances.isEmpty()) {
            nodeVo.setIsActive(false);
//            nodeVo.setNodeStatus(ProcessConstants.STATUS_WAITING);
            return nodeVo;
        }

        // 设置时间信息
        nodeVo.setCreateTime(callActivityInstances.stream()
                .map(HistoricActivityInstance::getStartTime)
                .min(Date::compareTo).orElse(null));

        // 判断整体状态
        boolean allCompleted = callActivityInstances.stream().allMatch(h -> h.getEndTime() != null);
        boolean anyStarted = callActivityInstances.stream().anyMatch(h -> h.getStartTime() != null);

        if (allCompleted) {
            nodeVo.setIsActive(false);
//            nodeVo.setNodeStatus(ProcessConstants.STATUS_FINISH);
            nodeVo.setEndTime(callActivityInstances.stream()
                    .map(HistoricActivityInstance::getEndTime)
                    .filter(Objects::nonNull)
                    .max(Date::compareTo).orElse(null));
        } else if (anyStarted) {
            nodeVo.setIsActive(true);
//            nodeVo.setNodeStatus(ProcessConstants.STATUS_RUNNING);
        } else {
            nodeVo.setIsActive(false);
//            nodeVo.setNodeStatus(ProcessConstants.STATUS_WAITING);
        }

        // 设置子流程实例信息
        List<WfSubProcessInstanceVo> subProcessInstances = new ArrayList<>();
        for (HistoricActivityInstance instance : callActivityInstances) {
            WfSubProcessInstanceVo subInstanceVo = new WfSubProcessInstanceVo();
            subInstanceVo.setExecutionId(instance.getExecutionId());
            subInstanceVo.setStartTime(instance.getStartTime());
            subInstanceVo.setEndTime(instance.getEndTime());
            
            // 获取部门信息
            HistoricVariableInstance historicVariableInstance = historyService.createHistoricVariableInstanceQuery()
                    .processInstanceId(historicProcIns.getId())
                    .variableName(elementVariable)
                    .executionId(instance.getExecutionId())
                    .singleResult();
            if (historicVariableInstance != null) {
                subInstanceVo.setDeptCode(historicVariableInstance.getValue().toString());
                subInstanceVo.setDeptName("部门_" + historicVariableInstance.getValue().toString());
            }

            // 查找对应的子流程实例
            List<ProcessInstance> subProcesses = runtimeService.createProcessInstanceQuery()
                    .superProcessInstanceId(instance.getProcessInstanceId())
                    .list();

            if (!subProcesses.isEmpty()) {
                ProcessInstance subProcess = subProcesses.get(0);
                subInstanceVo.setProcessInstanceId(subProcess.getId());
                subInstanceVo.setProcessInstanceName(subProcess.getName());
            } else {
                // 查找历史子流程实例
                List<HistoricProcessInstance> historicSubProcesses = historyService.createHistoricProcessInstanceQuery()
                        .superProcessInstanceId(instance.getProcessInstanceId())
                        .list();
                if (!historicSubProcesses.isEmpty()) {
                    HistoricProcessInstance historicSubProcess = historicSubProcesses.get(0);
                    subInstanceVo.setProcessInstanceId(historicSubProcess.getId());
                    subInstanceVo.setProcessInstanceName(historicSubProcess.getName());
                }
            }

            subProcessInstances.add(subInstanceVo);
        }

        nodeVo.setSubProcessInstances(subProcessInstances);
        return nodeVo;
    }

    @Override
    public TableDataInfo<WfSubProcessInstanceVo> selectPageCallActivityInstances(String procInstId, String callActivityId, PageQuery pageQuery) {
        // 查询调用活动的历史实例
        HistoricActivityInstanceQuery query = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(procInstId)
                .activityId(callActivityId)
                .activityType(BpmnXMLConstants.ELEMENT_CALL_ACTIVITY)
                .orderByHistoricActivityInstanceStartTime().asc();

        long total = query.count();
        if (total == 0) {
            return TableDataInfo.build();
        }

        int offset = pageQuery.getPageSize() * (pageQuery.getPageNum() - 1);
        List<HistoricActivityInstance> instances = query.listPage(offset, pageQuery.getPageSize());

        List<WfSubProcessInstanceVo> resultList = new ArrayList<>();
        for (HistoricActivityInstance instance : instances) {
            WfSubProcessInstanceVo vo = new WfSubProcessInstanceVo();
            vo.setExecutionId(instance.getExecutionId());
            vo.setStartTime(instance.getStartTime());
            vo.setEndTime(instance.getEndTime());

            // 查找对应的子流程实例
            List<ProcessInstance> subProcesses = runtimeService.createProcessInstanceQuery()
                    .superProcessInstanceId(instance.getProcessInstanceId())
                    .list();

            if (!subProcesses.isEmpty()) {
                ProcessInstance subProcess = subProcesses.get(0);
                vo.setProcessInstanceId(subProcess.getId());
                vo.setProcessInstanceName(subProcess.getName());
                vo.setInstanceStatus("running");
                vo.setIsActive(true);
            } else {
                // 查找历史子流程实例
                List<HistoricProcessInstance> historicSubProcesses = historyService.createHistoricProcessInstanceQuery()
                        .superProcessInstanceId(instance.getProcessInstanceId())
                        .list();
                if (!historicSubProcesses.isEmpty()) {
                    HistoricProcessInstance historicSubProcess = historicSubProcesses.get(0);
                    vo.setProcessInstanceId(historicSubProcess.getId());
                    vo.setProcessInstanceName(historicSubProcess.getName());
                    vo.setInstanceStatus("completed");
                    vo.setIsActive(false);
                }
            }

            resultList.add(vo);
        }

        TableDataInfo<WfSubProcessInstanceVo> result = new TableDataInfo<>();
        result.setRows(resultList);
        result.setTotal(total);
        return result;
    }

    @Override
    public WfSubProcessInstanceVo queryCallActivityInstanceDetails(String procInstId, String subProcessInstanceId) {
        // 查找子流程实例
        ProcessInstance subProcess = runtimeService.createProcessInstanceQuery()
                .processInstanceId(subProcessInstanceId)
                .singleResult();

        if (subProcess == null) {
            // 查找历史子流程实例
            HistoricProcessInstance historicSubProcess = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(subProcessInstanceId)
                    .singleResult();
            
            if (historicSubProcess == null) {
                throw new RestException("未找到指定的子流程实例: " + subProcessInstanceId);
            }
            
            return buildSubProcessInstanceVoFromHistoric(historicSubProcess);
        }

        return buildSubProcessInstanceVoFromRuntime(subProcess);
    }

    /**
     * 从运行时子流程实例构建VO
     */
    private WfSubProcessInstanceVo buildSubProcessInstanceVoFromRuntime(ProcessInstance subProcess) {
        WfSubProcessInstanceVo vo = new WfSubProcessInstanceVo();
        vo.setProcessInstanceId(subProcess.getId());
        vo.setProcessInstanceName(subProcess.getName());
        vo.setStartTime(subProcess.getStartTime());
        vo.setInstanceStatus("running");
        vo.setIsActive(true);

        // 获取子流程中的任务
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(subProcess.getId())
                .list();

        List<WfTaskVo> taskVos = new ArrayList<>();
        for (Task task : tasks) {
            WfTaskVo taskVo = new WfTaskVo();
            taskVo.setTaskId(task.getId());
            taskVo.setTaskName(task.getName());
            taskVo.setTaskDefKey(task.getTaskDefinitionKey());
            taskVo.setAssigneeId(task.getAssignee());
            taskVo.setCreateTime(task.getCreateTime());
            taskVos.add(taskVo);
        }

        // 注意：WfSubProcessInstanceVo中没有tasks字段，需要使用taskNodes
        // 这里需要转换为WfProcNodeVo列表
        List<WfProcNodeVo> taskNodes = new ArrayList<>();
        for (WfTaskVo taskVo : taskVos) {
            WfProcNodeVo nodeVo = new WfProcNodeVo();
            nodeVo.setActivityId(taskVo.getTaskDefKey());
            nodeVo.setActivityName(taskVo.getTaskName());
            nodeVo.setAssigneeId(taskVo.getAssigneeId());
            nodeVo.setCreateTime(taskVo.getCreateTime());
            nodeVo.setIsActive(true);
            taskNodes.add(nodeVo);
        }
        vo.setTaskNodes(taskNodes);

        return vo;
    }

    /**
     * 从历史子流程实例构建VO
     */
    private WfSubProcessInstanceVo buildSubProcessInstanceVoFromHistoric(HistoricProcessInstance historicSubProcess) {
        WfSubProcessInstanceVo vo = new WfSubProcessInstanceVo();
        vo.setProcessInstanceId(historicSubProcess.getId());
        vo.setProcessInstanceName(historicSubProcess.getName());
        vo.setStartTime(historicSubProcess.getStartTime());
        vo.setEndTime(historicSubProcess.getEndTime());
        vo.setInstanceStatus("completed");
        vo.setIsActive(false);

        // 获取子流程中的历史任务
        List<HistoricTaskInstance> historicTasks = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(historicSubProcess.getId())
                .orderByHistoricTaskInstanceStartTime()
                .asc()
                .list();

        // 转换为WfProcNodeVo列表
        List<WfProcNodeVo> taskNodes = new ArrayList<>();
        for (HistoricTaskInstance historicTask : historicTasks) {
            WfProcNodeVo nodeVo = new WfProcNodeVo();
            nodeVo.setActivityId(historicTask.getTaskDefinitionKey());
            nodeVo.setActivityName(historicTask.getName());
            nodeVo.setAssigneeId(historicTask.getAssignee());
            nodeVo.setCreateTime(historicTask.getStartTime());
            nodeVo.setEndTime(historicTask.getEndTime());
            nodeVo.setIsActive(false);
            taskNodes.add(nodeVo);
        }
        vo.setTaskNodes(taskNodes);

        return vo;
    }

    @Override
    public boolean addSignDepts(String procInstId, List<String> deptCodes) {
        return docFlowV3BusinessHandler.addSignDeptsByCode(procInstId, deptCodes);
    }

    @Override
    public boolean removeSignDepts(String procInstId, List<String> deptCodes) {
        return docFlowV3BusinessHandler.removeSignDeptsByCode(procInstId, deptCodes);
    }

    /**
     * 查询流程详情信息（优化版本）
     * 解决节点顺序错乱、冗余数据展示及子流程实例任务联动问题
     */
    @Override
    public List<WfProcNodeVo> queryProcessDetailsWithProcessedSubProcessHandling(String procInstId) {
        // 参数验证
        if (StringUtils.isBlank(procInstId)) {
            throw new RestException("流程实例ID不能为空");
        }

        // 获取流程实例
        HistoricProcessInstance historicProcIns = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(procInstId)
                .includeProcessVariables()
                .singleResult();
        if (historicProcIns == null) {
            throw new RestException("流程实例不存在");
        }

        // 获取BPMN模型
        BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcIns.getProcessDefinitionId());
        Process mainProcess = bpmnModel.getMainProcess();

        // 获取所有历史活动实例
        List<HistoricActivityInstance> allHistoricActivities = historyService.createHistoricActivityInstanceQuery()
                .processInstanceId(procInstId)
                .orderByHistoricActivityInstanceStartTime()
                .asc()
                .list();

        // 获取所有活跃任务
        List<Task> allActiveTasks = taskService.createTaskQuery()
                .processInstanceId(procInstId)
                .active()
                .list();

        // 判断流程实例状态
        String processStatus = determineProcessInstanceStatus(historicProcIns, allActiveTasks);
        boolean isCompleted = "completed".equals(processStatus);

        List<WfProcNodeVo> nodeList = new ArrayList<>();

        // 严格按流程定义顺序处理所有流程元素
        Collection<FlowElement> flowElements = mainProcess.getFlowElements();
        for (FlowElement flowElement : flowElements) {
            if (flowElement instanceof UserTask) {
                WfProcNodeVo userTaskNode = processOptimizedUserTaskNode((UserTask) flowElement,
                    allHistoricActivities, allActiveTasks, isCompleted);
                if (userTaskNode != null) {
                    nodeList.add(userTaskNode);
                }
            } else if (flowElement instanceof CallActivity) {
                WfProcNodeVo callActivityNode = processSimplifiedCallActivityNode((CallActivity) flowElement,
                    historicProcIns, allHistoricActivities, isCompleted);
                if (callActivityNode != null) {
                    nodeList.add(callActivityNode);
                }
            }
            // 跳过网关等非业务节点（特别是已完成流程）
        }

        // 如果是已完成流程，过滤未执行的节点
        if (isCompleted) {
            nodeList = filterExecutedNodesForCompletedProcess(nodeList, allHistoricActivities);
        }

        return nodeList;
    }
