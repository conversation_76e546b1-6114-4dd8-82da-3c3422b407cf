<?xml version="1.0" encoding="UTF-8"?>
<bpmn2:definitions xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bpmn2="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:flowable="http://flowable.org/bpmn" id="diagram_Process_0531_v3" targetNamespace="http://flowable.org/bpmn" xsi:schemaLocation="http://www.omg.org/spec/BPMN/20100524/MODEL BPMN20.xsd">
    <bpmn2:process id="doc_flow_v3" name="公文流转V3" isExecutable="true">
        <bpmn2:startEvent id="Event_start">
            <bpmn2:outgoing>Flow_start_to_submit</bpmn2:outgoing>
        </bpmn2:startEvent>
        <bpmn2:userTask id="Activity_submit" name="发起公文流转" flowable:formKey="key_1906235449754161153" flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:documentation></bpmn2:documentation>
            <bpmn2:extensionElements>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment" />
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_start_to_submit</bpmn2:incoming>
            <bpmn2:outgoing>Flow_submit_to_deputy</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_deputy_director" name="副主任复核" flowable:formKey="key_1906235449754161153" flowable:dataType="DEPT:AND:POST" flowable:assignee="${assignee}" flowable:candidateGroups="DEPT10101,POSTDeputyHead" flowable:text="校办副主任">
            <bpmn2:extensionElements>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment" />
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_submit_to_deputy</bpmn2:incoming>
            <bpmn2:outgoing>Flow_deputy_to_doc_manager</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics flowable:collection="${docFlowWfHandler.getUserIds(execution)}" flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">${nrOfCompletedInstances &gt; 0}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_doc_manager" name="选择校领导" flowable:formKey="key_1906235449754161153" flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="公文负责人">
            <bpmn2:extensionElements>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment" />
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_deputy_to_doc_manager</bpmn2:incoming>
            <bpmn2:outgoing>Flow_doc_manager_to_school_leader</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_school_leader" name="校领导审批" flowable:formKey="key_1906235449754161153" flowable:dataType="POST" flowable:assignee="${assignee.username}" flowable:candidateGroups="ROLEPresident" flowable:text="校领导">
            <bpmn2:extensionElements>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment" />
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_doc_manager_to_school_leader</bpmn2:incoming>
            <bpmn2:outgoing>Flow_school_leader_to_office_pre</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics isSequential="true" flowable:collection="${Activity_school_leader}" flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_office_handler_pre" name="分发业务部门" flowable:formKey="key_1906235449754161153" flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment" />
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_school_leader_to_office_pre</bpmn2:incoming>
            <bpmn2:outgoing>Flow_office_pre_to_call_activity</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_office_handler" name="公文负责人" flowable:formKey="key_1906235449754161153" flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment" />
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_call_activity_to_office_handler</bpmn2:incoming>
            <bpmn2:outgoing>Flow_office_handler_to_gateway</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:exclusiveGateway id="Gateway_need_feedback" name="是否需要反馈">
            <bpmn2:incoming>Flow_office_handler_to_gateway</bpmn2:incoming>
            <bpmn2:outgoing>Flow_need_feedback</bpmn2:outgoing>
            <bpmn2:outgoing>Flow_no_feedback</bpmn2:outgoing>
        </bpmn2:exclusiveGateway>
        <bpmn2:userTask id="Activity_dept_liaison" name="部门联络员处理" flowable:formKey="key_1906235449754161153" flowable:dataType="CCDTO" flowable:assignee="${assignee.username}" flowable:text="${assignee.deptName}">
            <bpmn2:extensionElements>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment" />
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_need_feedback</bpmn2:incoming>
            <bpmn2:outgoing>Flow_liaison_to_final_doc_manager</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics flowable:collection="${docFlowV3WfHandler.getDeptContactsForCallActivity(execution, Activity_dept_call_activity)}" flowable:elementVariable="assignee">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${nrOfCompletedInstances &gt;= nrOfInstances}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:userTask>
        <bpmn2:userTask id="Activity_final_doc_manager" name="公文归档" flowable:formKey="key_1906235449754161153" flowable:dataType="INITIATOR" flowable:assignee="${initiator}" flowable:text="流程发起人">
            <bpmn2:extensionElements>
                <flowable:taskListener expression="${docFlowV3WfHandler.updateStatus(execution)}" event="assignment" />
                <flowable:taskListener expression="${docFlowV3WfHandler.notify(task)}" event="assignment" />
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_liaison_to_final_doc_manager</bpmn2:incoming>
            <bpmn2:outgoing>Flow_final_to_end</bpmn2:outgoing>
        </bpmn2:userTask>
        <bpmn2:endEvent id="Event_end">
            <bpmn2:incoming>Flow_no_feedback</bpmn2:incoming>
            <bpmn2:incoming>Flow_final_to_end</bpmn2:incoming>
        </bpmn2:endEvent>
        <bpmn2:sequenceFlow id="Flow_start_to_submit" sourceRef="Event_start" targetRef="Activity_submit" />
        <bpmn2:sequenceFlow id="Flow_submit_to_deputy" sourceRef="Activity_submit" targetRef="Activity_deputy_director" />
        <bpmn2:sequenceFlow id="Flow_deputy_to_doc_manager" sourceRef="Activity_deputy_director" targetRef="Activity_doc_manager" />
        <bpmn2:sequenceFlow id="Flow_doc_manager_to_school_leader" sourceRef="Activity_doc_manager" targetRef="Activity_school_leader" />
        <bpmn2:sequenceFlow id="Flow_school_leader_to_office_pre" sourceRef="Activity_school_leader" targetRef="Activity_office_handler_pre" />
        <bpmn2:sequenceFlow id="Flow_office_pre_to_call_activity" sourceRef="Activity_office_handler_pre" targetRef="Activity_dept_call_activity" />
        <bpmn2:sequenceFlow id="Flow_call_activity_to_office_handler" sourceRef="Activity_dept_call_activity" targetRef="Activity_office_handler" />
        <bpmn2:sequenceFlow id="Flow_office_handler_to_gateway" sourceRef="Activity_office_handler" targetRef="Gateway_need_feedback" />
        <bpmn2:sequenceFlow id="Flow_need_feedback" name="需要反馈" sourceRef="Gateway_need_feedback" targetRef="Activity_dept_liaison">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needFeedback == true}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="Flow_no_feedback" name="不需要反馈" sourceRef="Gateway_need_feedback" targetRef="Event_end">
            <bpmn2:conditionExpression xsi:type="bpmn2:tFormalExpression">${needFeedback == false}</bpmn2:conditionExpression>
        </bpmn2:sequenceFlow>
        <bpmn2:sequenceFlow id="Flow_liaison_to_final_doc_manager" sourceRef="Activity_dept_liaison" targetRef="Activity_final_doc_manager" />
        <bpmn2:sequenceFlow id="Flow_final_to_end" sourceRef="Activity_final_doc_manager" targetRef="Event_end" />
        <!-- 调用子流程活动 - 替换原来的嵌套子流程 -->
        <bpmn2:callActivity id="Activity_dept_call_activity" name="业务部门处理" calledElement="doc_flow_v3_subprocess">
            <bpmn2:extensionElements>
                <flowable:documentation>departmentHandling</flowable:documentation>
                <!-- 输入参数映射 - 传递当前部门负责人和其他必要参数 -->
                <flowable:in source="${deptLeader.deptCode}" target="deptCode"/>
                <flowable:in source="deptLeader" target="deptLeader"/>
                <flowable:in source="docType" target="docType"/>
                <flowable:in source="creator" target="creator"/>
                <flowable:in source="initiator" target="initiator"/>
                <flowable:in source="docTitle" target="docTitle"/>
                <flowable:in source="priorityLevel" target="priorityLevel"/>
                <flowable:in source="processStatus" target="processStatus"/>
                <flowable:in source="status" target="status"/>
                <flowable:in source="isRemind" target="isRemind"/>
                <flowable:in source="isPublic" target="isPublic"/>
                <flowable:in source="id" target="id"/>
                <flowable:in source="${execution.processInstanceId}" target="parentProcessInstanceId"/>
                <flowable:in source="Activity_dept_call_activity" target="Activity_dept_call_activity"/>
                <!-- 输出参数映射 -->
                <flowable:out source="deptResult" target="deptResult"/>
                <flowable:out source="deptHeadResult" target="deptHeadResult"/>
                <flowable:out source="needFeedback" target="needFeedback"/>
            </bpmn2:extensionElements>
            <bpmn2:incoming>Flow_office_pre_to_call_activity</bpmn2:incoming>
            <bpmn2:outgoing>Flow_call_activity_to_office_handler</bpmn2:outgoing>
            <bpmn2:multiInstanceLoopCharacteristics
                    flowable:collection="${docFlowV3WfHandler.getDynamicDepts(execution, Activity_dept_call_activity)}"
                    flowable:elementVariable="deptLeader">
                <bpmn2:completionCondition xsi:type="bpmn2:tFormalExpression">
                    ${docFlowV3WfHandler.checkCallActivityCompletion(execution, nrOfCompletedInstances, nrOfInstances, nrOfActiveInstances)}
                </bpmn2:completionCondition>
            </bpmn2:multiInstanceLoopCharacteristics>
        </bpmn2:callActivity>
    </bpmn2:process>
    <bpmndi:BPMNDiagram id="BPMNDiagram_v3">
        <bpmndi:BPMNPlane id="BPMNPlane_v3" bpmnElement="doc_flow_v3">
            <bpmndi:BPMNEdge id="Flow_final_to_end_di" bpmnElement="Flow_final_to_end">
                <di:waypoint x="1550" y="240" />
                <di:waypoint x="1550" y="270" />
                <di:waypoint x="1572" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_liaison_to_final_doc_manager_di" bpmnElement="Flow_liaison_to_final_doc_manager">
                <di:waypoint x="1450" y="200" />
                <di:waypoint x="1500" y="200" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_no_feedback_di" bpmnElement="Flow_no_feedback">
                <di:waypoint x="1305" y="270" />
                <di:waypoint x="1572" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_need_feedback_di" bpmnElement="Flow_need_feedback">
                <di:waypoint x="1280" y="245" />
                <di:waypoint x="1280" y="200" />
                <di:waypoint x="1350" y="200" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_office_handler_to_gateway_di" bpmnElement="Flow_office_handler_to_gateway">
                <di:waypoint x="1200" y="270" />
                <di:waypoint x="1255" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_call_activity_to_office_handler_di" bpmnElement="Flow_call_activity_to_office_handler">
                <di:waypoint x="1050" y="270" />
                <di:waypoint x="1100" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_office_pre_to_call_activity_di" bpmnElement="Flow_office_pre_to_call_activity">
                <di:waypoint x="900" y="270" />
                <di:waypoint x="950" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_school_leader_to_office_pre_di" bpmnElement="Flow_school_leader_to_office_pre">
                <di:waypoint x="760" y="270" />
                <di:waypoint x="800" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_doc_manager_to_school_leader_di" bpmnElement="Flow_doc_manager_to_school_leader">
                <di:waypoint x="620" y="270" />
                <di:waypoint x="660" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_deputy_to_doc_manager_di" bpmnElement="Flow_deputy_to_doc_manager">
                <di:waypoint x="480" y="270" />
                <di:waypoint x="520" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_submit_to_deputy_di" bpmnElement="Flow_submit_to_deputy">
                <di:waypoint x="340" y="270" />
                <di:waypoint x="380" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="Flow_start_to_submit_di" bpmnElement="Flow_start_to_submit">
                <di:waypoint x="208" y="270" />
                <di:waypoint x="240" y="270" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNShape id="Event_start_di" bpmnElement="Event_start">
                <dc:Bounds x="172" y="252" width="36" height="36" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_submit_di" bpmnElement="Activity_submit">
                <dc:Bounds x="240" y="230" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_deputy_director_di" bpmnElement="Activity_deputy_director">
                <dc:Bounds x="380" y="230" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_doc_manager_di" bpmnElement="Activity_doc_manager">
                <dc:Bounds x="520" y="230" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_school_leader_di" bpmnElement="Activity_school_leader">
                <dc:Bounds x="660" y="230" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_office_handler_pre_di" bpmnElement="Activity_office_handler_pre">
                <dc:Bounds x="800" y="230" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_office_handler_di" bpmnElement="Activity_office_handler">
                <dc:Bounds x="1100" y="230" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Gateway_need_feedback_di" bpmnElement="Gateway_need_feedback" isMarkerVisible="true">
                <dc:Bounds x="1255" y="245" width="50" height="50" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_dept_liaison_di" bpmnElement="Activity_dept_liaison">
                <dc:Bounds x="1350" y="160" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_final_doc_manager_di" bpmnElement="Activity_final_doc_manager">
                <dc:Bounds x="1500" y="160" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Event_end_di" bpmnElement="Event_end">
                <dc:Bounds x="1572" y="252" width="36" height="36" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="Activity_0p81art_di" bpmnElement="Activity_dept_call_activity" isExpanded="true">
                <dc:Bounds x="950" y="230" width="100" height="80" />
            </bpmndi:BPMNShape>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
</bpmn2:definitions>